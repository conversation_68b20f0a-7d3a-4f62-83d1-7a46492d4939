{"name": "audio-demo", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-native-community/slider": "4.5.6", "@react-navigation/native": "^7.1.16", "@react-navigation/native-stack": "^7.3.23", "expo": "~53.0.20", "expo-audio": "~0.4.8", "expo-document-picker": "~13.1.6", "expo-linear-gradient": "~14.1.5", "expo-sharing": "~13.1.5", "expo-status-bar": "~2.2.3", "lottie-react-native": "7.2.2", "react": "19.0.0", "react-native": "0.79.5", "react-native-linear-gradient": "^2.8.3", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "expo-av": "~15.1.7", "expo-file-system": "~18.1.11", "react-native-svg": "15.11.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}