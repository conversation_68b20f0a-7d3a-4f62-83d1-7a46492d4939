import React, { useEffect, useRef } from 'react';
import { Animated, StyleSheet, Dimensions, View } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Colors } from '../constants/DesignSystem';

const { width, height } = Dimensions.get('window');

interface Props {
  children: React.ReactNode;
}

export default function WaveBackground({ children }: Props) {
  // 创建多个波浪层的动画值
  const wave1Y = useRef(new Animated.Value(0)).current;
  const wave2Y = useRef(new Animated.Value(0)).current;
  const wave3Y = useRef(new Animated.Value(0)).current;

  // 波浪透明度动画 - 提高初始透明度使其可见
  const wave1Opacity = useRef(new Animated.Value(0.3)).current;
  const wave2Opacity = useRef(new Animated.Value(0.25)).current;
  const wave3Opacity = useRef(new Animated.Value(0.2)).current;

  // 背景渐变透明度动画
  const backgroundOpacity = useRef(new Animated.Value(0.4)).current;

  // 调试：组件挂载时打印日志
  console.log('WaveBackground mounted, screen dimensions:', { width, height });

  useEffect(() => {
    // 波浪1 - 最慢的上下浮动
    const wave1Animation = Animated.loop(
      Animated.sequence([
        Animated.timing(wave1Y, {
          toValue: -15,
          duration: 4000,
          useNativeDriver: true,
        }),
        Animated.timing(wave1Y, {
          toValue: 15,
          duration: 4000,
          useNativeDriver: true,
        }),
      ])
    );

    // 波浪2 - 中等速度
    const wave2Animation = Animated.loop(
      Animated.sequence([
        Animated.timing(wave2Y, {
          toValue: 12,
          duration: 3200,
          useNativeDriver: true,
        }),
        Animated.timing(wave2Y, {
          toValue: -12,
          duration: 3200,
          useNativeDriver: true,
        }),
      ])
    );

    // 波浪3 - 最快的浮动
    const wave3Animation = Animated.loop(
      Animated.sequence([
        Animated.timing(wave3Y, {
          toValue: 8,
          duration: 2500,
          useNativeDriver: true,
        }),
        Animated.timing(wave3Y, {
          toValue: -8,
          duration: 2500,
          useNativeDriver: true,
        }),
      ])
    );

    // 透明度动画 - 提高透明度范围使波浪更可见
    const opacity1Animation = Animated.loop(
      Animated.sequence([
        Animated.timing(wave1Opacity, {
          toValue: 0.4,
          duration: 3000,
          useNativeDriver: true,
        }),
        Animated.timing(wave1Opacity, {
          toValue: 0.2,
          duration: 3000,
          useNativeDriver: true,
        }),
      ])
    );

    const opacity2Animation = Animated.loop(
      Animated.sequence([
        Animated.timing(wave2Opacity, {
          toValue: 0.35,
          duration: 2800,
          useNativeDriver: true,
        }),
        Animated.timing(wave2Opacity, {
          toValue: 0.15,
          duration: 2800,
          useNativeDriver: true,
        }),
      ])
    );

    const opacity3Animation = Animated.loop(
      Animated.sequence([
        Animated.timing(wave3Opacity, {
          toValue: 0.3,
          duration: 2200,
          useNativeDriver: true,
        }),
        Animated.timing(wave3Opacity, {
          toValue: 0.1,
          duration: 2200,
          useNativeDriver: true,
        }),
      ])
    );

    // 背景透明度缓慢变化
    const backgroundAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(backgroundOpacity, {
          toValue: 0.6,
          duration: 5000,
          useNativeDriver: true,
        }),
        Animated.timing(backgroundOpacity, {
          toValue: 0.4,
          duration: 5000,
          useNativeDriver: true,
        }),
      ])
    );

    // 启动所有动画
    console.log('Starting wave animations...');
    wave1Animation.start();
    wave2Animation.start();
    wave3Animation.start();
    opacity1Animation.start();
    opacity2Animation.start();
    opacity3Animation.start();
    backgroundAnimation.start();

    return () => {
      wave1Animation.stop();
      wave2Animation.stop();
      wave3Animation.stop();
      opacity1Animation.stop();
      opacity2Animation.stop();
      opacity3Animation.stop();
      backgroundAnimation.stop();
    };
  }, []);

  return (
    <View style={styles.container}>
      {/* 基础渐变背景 */}
      <Animated.View style={[StyleSheet.absoluteFill, { opacity: backgroundOpacity }]}>
        <LinearGradient
          colors={[Colors.background.gradient.start, Colors.background.gradient.end, '#F0E6FF']}
          style={StyleSheet.absoluteFill}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
      </Animated.View>

      {/* 波浪层 - 使用椭圆形状模拟波浪 */}
      <View style={[StyleSheet.absoluteFill, { zIndex: 1 }]} pointerEvents="none">
        {/* 波浪1 - 最大最慢 */}
        <Animated.View
          style={[
            styles.wave,
            styles.wave1,
            {
              opacity: wave1Opacity,
              transform: [{ translateY: wave1Y }],
            },
          ]}
        />

        {/* 波浪2 - 中等 */}
        <Animated.View
          style={[
            styles.wave,
            styles.wave2,
            {
              opacity: wave2Opacity,
              transform: [{ translateY: wave2Y }],
            },
          ]}
        />

        {/* 波浪3 - 最小最快 */}
        <Animated.View
          style={[
            styles.wave,
            styles.wave3,
            {
              opacity: wave3Opacity,
              transform: [{ translateY: wave3Y }],
            },
          ]}
        />

        {/* 添加一些额外的装饰波浪点 */}
        <Animated.View
          style={[
            styles.decorativeWave,
            {
              opacity: wave1Opacity,
              transform: [{ translateY: wave1Y }, { translateX: wave2Y }],
            },
          ]}
        />
        <Animated.View
          style={[
            styles.decorativeWave2,
            {
              opacity: wave2Opacity,
              transform: [{ translateY: wave2Y }, { translateX: wave3Y }],
            },
          ]}
        />
      </View>

      {/* 内容层 */}
      <View style={[StyleSheet.absoluteFill, { zIndex: 2 }]}>
        {children}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  wave: {
    position: 'absolute',
    borderRadius: width,
  },
  wave1: {
    width: width * 2.5,
    height: width * 2.5,
    backgroundColor: '#FFB6C1', // 使用纯色，透明度由Animated.Value控制
    bottom: -width * 1.2, // 进一步调整位置使更多部分可见
    left: -width * 0.75,
  },
  wave2: {
    width: width * 2.2,
    height: width * 2.2,
    backgroundColor: '#FFD7E5', // 使用纯色
    bottom: -width * 1.0, // 进一步调整位置
    left: -width * 0.6,
  },
  wave3: {
    width: width * 1.8,
    height: width * 1.8,
    backgroundColor: '#F0E6FF', // 使用纯色
    bottom: -width * 0.8, // 进一步调整位置使其更可见
    left: -width * 0.4,
  },
  decorativeWave: {
    position: 'absolute',
    width: width * 0.3,
    height: width * 0.3,
    backgroundColor: '#FFB6C1',
    borderRadius: width * 0.15,
    top: height * 0.2,
    right: width * 0.1,
  },
  decorativeWave2: {
    position: 'absolute',
    width: width * 0.2,
    height: width * 0.2,
    backgroundColor: '#FFD7E5',
    borderRadius: width * 0.1,
    top: height * 0.6,
    left: width * 0.05,
  },
});
