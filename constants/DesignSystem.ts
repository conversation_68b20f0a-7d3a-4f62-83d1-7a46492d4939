// Design System - 统一的设计规范
export const Colors = {
  // 主色调 - 温暖的粉色系
  primary: {
    50: '#FFF5F7',
    100: '#FFE9F2',
    200: '#FFD7E5',
    300: '#FFB6C1',
    400: '#FF9FAD',
    500: '#FF8A9B',
    600: '#E6758A',
    700: '#CC6079',
    800: '#B34B68',
    900: '#993657',
  },
  
  // 辅助色 - 柔和的紫色
  secondary: {
    50: '#F3F4FF',
    100: '#E8EAFF',
    200: '#D1D5FF',
    300: '#A5ACFF',
    400: '#7983FF',
    500: '#5C6BC0',
    600: '#4A5AAF',
    700: '#38499E',
    800: '#26388D',
    900: '#14277C',
  },
  
  // 中性色
  neutral: {
    50: '#FAFAFA',
    100: '#F5F5F5',
    200: '#EEEEEE',
    300: '#E0E0E0',
    400: '#BDBDBD',
    500: '#9E9E9E',
    600: '#757575',
    700: '#616161',
    800: '#424242',
    900: '#212121',
  },
  
  // 语义色
  success: '#4CAF50',
  warning: '#FF9800',
  error: '#F44336',
  info: '#2196F3',
  
  // 背景色
  background: {
    primary: '#FFFFFF',
    secondary: '#FAFAFA',
    tertiary: 'rgba(255,255,255,0.8)',
    overlay: 'rgba(255,255,255,0.6)',
    gradient: {
      start: '#FFE9F2',
      end: '#FFD7E5',
    },
  },
  
  // 文本色
  text: {
    primary: '#212121',
    secondary: '#616161',
    tertiary: '#9E9E9E',
    inverse: '#FFFFFF',
    placeholder: '#BDBDBD',
  },
};

export const Typography = {
  // 字体大小
  fontSize: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
    '5xl': 48,
  },
  
  // 行高
  lineHeight: {
    tight: 1.25,
    normal: 1.5,
    relaxed: 1.75,
  },
  
  // 字重
  fontWeight: {
    normal: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const,
  },
};

export const Spacing = {
  // 间距系统 (4px 基准)
  0: 0,
  1: 4,
  2: 8,
  3: 12,
  4: 16,
  5: 20,
  6: 24,
  8: 32,
  10: 40,
  12: 48,
  16: 64,
  20: 80,
  24: 96,
};

export const BorderRadius = {
  none: 0,
  sm: 4,
  base: 8,
  md: 12,
  lg: 16,
  xl: 20,
  '2xl': 24,
  full: 9999,
};

export const Shadows = {
  none: {
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  base: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 16,
  },
};

export const Layout = {
  // 容器最大宽度
  maxWidth: {
    sm: 384,
    md: 448,
    lg: 512,
    xl: 576,
  },
  
  // 屏幕断点
  breakpoints: {
    sm: 640,
    md: 768,
    lg: 1024,
    xl: 1280,
  },
};

// 动画配置
export const Animation = {
  duration: {
    fast: 150,
    normal: 300,
    slow: 500,
    slower: 1000,
  },
  
  easing: {
    linear: 'linear',
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',
  },
};

// 组件样式预设
export const ComponentStyles = {
  // 按钮样式
  button: {
    primary: {
      backgroundColor: Colors.primary[300],
      borderRadius: BorderRadius.base,
      paddingHorizontal: Spacing[6],
      paddingVertical: Spacing[3],
      ...Shadows.sm,
    },
    secondary: {
      backgroundColor: Colors.background.tertiary,
      borderRadius: BorderRadius.base,
      paddingHorizontal: Spacing[6],
      paddingVertical: Spacing[3],
      borderWidth: 1,
      borderColor: Colors.primary[300],
      ...Shadows.sm,
    },
    disabled: {
      backgroundColor: Colors.neutral[300],
      borderRadius: BorderRadius.base,
      paddingHorizontal: Spacing[6],
      paddingVertical: Spacing[3],
    },
  },
  
  // 卡片样式
  card: {
    base: {
      backgroundColor: Colors.background.overlay,
      borderRadius: BorderRadius.md,
      padding: Spacing[4],
      ...Shadows.base,
    },
    elevated: {
      backgroundColor: Colors.background.primary,
      borderRadius: BorderRadius.md,
      padding: Spacing[4],
      ...Shadows.md,
    },
  },
  
  // 输入框样式
  input: {
    base: {
      backgroundColor: Colors.background.overlay,
      borderRadius: BorderRadius.base,
      padding: Spacing[3],
      borderWidth: 1,
      borderColor: Colors.neutral[300],
      fontSize: Typography.fontSize.base,
      color: Colors.text.primary,
    },
    focused: {
      borderColor: Colors.primary[400],
      ...Shadows.sm,
    },
  },
};
