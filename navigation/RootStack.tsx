import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

import SplashScreen from '../screens/SplashScreen';
import CategoryScreen from '../screens/CategoryScreen';
import RequirementFormScreen from '../screens/RequirementFormScreen';
import BGMScreen from '../screens/BGMScreen';
import CustomAudioScreen from '../screens/CustomAudioScreen';
import ProcessingScreen from '../screens/ProcessingScreen';
import ResultScreen from '../screens/ResultScreen';

export type RootStackParamList = {
  Splash: undefined;
  Category: undefined;
  Requirement: { categoryId: string } | undefined;
  BGM: undefined;
  CustomAudio: undefined;
  Processing: undefined;
  Result: undefined;
};

const Stack = createNativeStackNavigator<RootStackParamList>();

export default function RootStack() {
  return (
    <Stack.Navigator
      initialRouteName="Splash"
      screenOptions={{ headerShown: false }}
    >
      <Stack.Screen name="Splash" component={SplashScreen} />
      <Stack.Screen name="Category" component={CategoryScreen} />
            <Stack.Screen name="Requirement" component={RequirementFormScreen} />
      <Stack.Screen name="BGM" component={BGMScreen} />
      <Stack.Screen name="CustomAudio" component={CustomAudioScreen} />
      <Stack.Screen name="Processing" component={ProcessingScreen} />
      <Stack.Screen name="Result" component={ResultScreen} />
    </Stack.Navigator>
  );
}
