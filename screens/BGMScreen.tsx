import React, { useState, useRef } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Animated, ScrollView } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/RootStack';
import { Colors, Typography, Spacing, BorderRadius, Shadows, ComponentStyles } from '../constants/DesignSystem';

const bgmStyles = [
  { id: 'quiet', name: '安静', description: '纯净无扰的静谧空间', icon: '🤫', color: Colors.secondary[200] },
  { id: 'rhythm', name: '节奏', description: '轻柔律动的音乐节拍', icon: '🎵', color: Colors.primary[200] },
  { id: 'custom', name: '自定义', description: '可上传您喜欢的音乐', icon: '🎨', color: Colors.neutral[200] },
  { id: 'nature', name: '自然', description: '例如雨声的白噪音', icon: '🌿', color: '#E8F5E8' },
  { id: 'focus', name: '专注', description: '提升注意力的专业音频', icon: '🎯', color: '#E3F2FD' },
  { id: 'dream', name: '梦境', description: '引导进入深度放松状态', icon: '🌙', color: '#F3E5F5' },
];

type Props = NativeStackScreenProps<RootStackParamList, 'BGM'>;

export default function BGMScreen({ navigation }: Props) {
  const [selectedId, setSelectedId] = useState<string>();
  const animatedValues = useRef(
    bgmStyles.map(() => new Animated.Value(1))
  ).current;

  const handlePressIn = (index: number) => {
    Animated.spring(animatedValues[index], {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = (index: number) => {
    Animated.spring(animatedValues[index], {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  const handleSelect = (id: string, index: number) => {
    if (id === 'custom') {
      // 如果选择自定义，直接跳转到上传页面
      navigation.navigate('CustomAudio');
      return;
    }

    setSelectedId(id);
    // 选中时的反馈动画
    Animated.sequence([
      Animated.spring(animatedValues[index], {
        toValue: 1.05,
        useNativeDriver: true,
      }),
      Animated.spring(animatedValues[index], {
        toValue: 1,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const renderBGMCard = (item: typeof bgmStyles[0], index: number) => (
    <Animated.View
      key={item.id}
      style={[
        styles.cardContainer,
        {
          transform: [{ scale: animatedValues[index] }],
        },
      ]}
    >
      <TouchableOpacity
        style={[
          styles.card,
          { backgroundColor: item.color },
          item.id === selectedId && styles.cardActive,
        ]}
        onPressIn={() => handlePressIn(index)}
        onPressOut={() => handlePressOut(index)}
        onPress={() => handleSelect(item.id, index)}
        activeOpacity={0.8}
      >
        <Text style={styles.cardIcon}>{item.icon}</Text>
        <Text style={styles.cardTitle}>{item.name}</Text>
        <Text style={styles.cardDescription}>{item.description}</Text>
        {item.id === selectedId && (
          <View style={styles.selectedIndicator}>
            <Text style={styles.selectedIcon}>✓</Text>
          </View>
        )}
      </TouchableOpacity>
    </Animated.View>
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.headerContainer}>
        <Text style={styles.header}>选择您喜欢的音乐风格作为sub的bgm</Text>
        <Text style={styles.subheader}>不同场景的风格会对应不同的Hz叠加</Text>
      </View>

      <View style={styles.cardsContainer}>
        {bgmStyles.map((item, index) => renderBGMCard(item, index))}
      </View>

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[
            styles.generateButton,
            selectedId ? styles.generateButtonActive : styles.generateButtonDisabled,
          ]}
          disabled={!selectedId}
          onPress={() => navigation.navigate('Processing')}
        >
          <Text style={[
            styles.generateButtonText,
            selectedId ? styles.generateButtonTextActive : styles.generateButtonTextDisabled,
          ]}>
            生成音频
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: Spacing[6],
  },
  headerContainer: {
    alignItems: 'center',
    marginTop: Spacing[8],
    marginBottom: Spacing[8],
  },
  header: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.semibold,
    textAlign: 'center',
    color: Colors.text.primary,
    marginBottom: Spacing[2],
    lineHeight: Typography.lineHeight.normal * Typography.fontSize.xl,
  },
  subheader: {
    fontSize: Typography.fontSize.base,
    textAlign: 'center',
    color: Colors.text.secondary,
    lineHeight: Typography.lineHeight.relaxed * Typography.fontSize.base,
  },
  cardsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: Spacing[8],
  },
  cardContainer: {
    width: '48%',
    marginBottom: Spacing[4],
  },
  card: {
    borderRadius: BorderRadius.lg,
    padding: Spacing[4],
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 160,
    position: 'relative',
    ...Shadows.base,
  },
  cardActive: {
    borderWidth: 2,
    borderColor: Colors.primary[400],
    ...Shadows.md,
  },
  cardIcon: {
    fontSize: Typography.fontSize['2xl'],
    marginBottom: Spacing[2],
  },
  cardTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.text.primary,
    marginBottom: Spacing[1],
    textAlign: 'center',
  },
  cardDescription: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
    textAlign: 'center',
    lineHeight: Typography.lineHeight.normal * Typography.fontSize.sm,
  },
  selectedIndicator: {
    position: 'absolute',
    top: Spacing[2],
    right: Spacing[2],
    backgroundColor: Colors.primary[400],
    borderRadius: BorderRadius.full,
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectedIcon: {
    color: Colors.text.inverse,
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.bold,
  },
  buttonContainer: {
    alignItems: 'center',
    paddingVertical: Spacing[4],
  },
  generateButton: {
    borderRadius: BorderRadius.base,
    paddingHorizontal: Spacing[10],
    paddingVertical: Spacing[4],
    alignItems: 'center',
    minWidth: 200,
    ...Shadows.sm,
  },
  generateButtonActive: {
    backgroundColor: Colors.primary[400],
  },
  generateButtonDisabled: {
    backgroundColor: Colors.neutral[300],
  },
  generateButtonText: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
  },
  generateButtonTextActive: {
    color: Colors.text.inverse,
  },
  generateButtonTextDisabled: {
    color: Colors.text.tertiary,
  },
});
