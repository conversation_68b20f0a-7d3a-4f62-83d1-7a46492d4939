import React, { useRef } from 'react';
import { View, Text, FlatList, TouchableOpacity, StyleSheet, Animated } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/RootStack';
import { Colors, Typography, Spacing, BorderRadius, Shadows, ComponentStyles } from '../constants/DesignSystem';

const categories = [
  { id: 'sp', title: 'SP', subtitle: '新人/复合', icon: '✨' },
  { id: 'health', title: '健康', subtitle: '身心调理', icon: '🌿' },
  { id: 'biz', title: '外貌', subtitle: '五官身材', icon: '🤩' },
  { id: 'study', title: '学业', subtitle: '学习提升', icon: '📚' },
  { id: 'fun', title: '财富', subtitle: '金钱磁铁', icon: '💰' },
  { id: 'mix', title: '综合', subtitle: '多项愿景', icon: '🎭' },
];

type Props = NativeStackScreenProps<RootStackParamList, 'Category'>;

export default function CategoryScreen({ navigation }: Props) {
  const animatedValues = useRef(
    categories.map(() => new Animated.Value(1))
  ).current;

  const handlePressIn = (index: number) => {
    Animated.spring(animatedValues[index], {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = (index: number) => {
    Animated.spring(animatedValues[index], {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  const renderItem = ({ item, index }: { item: typeof categories[0]; index: number }) => (
    <Animated.View
      style={[
        styles.cardContainer,
        {
          transform: [{ scale: animatedValues[index] }],
        },
      ]}
    >
      <TouchableOpacity
        style={styles.card}
        onPressIn={() => handlePressIn(index)}
        onPressOut={() => handlePressOut(index)}
        onPress={() => navigation.navigate('Requirement', { categoryId: item.id })}
        activeOpacity={0.8}
      >
        <Text style={styles.cardIcon}>{item.icon}</Text>
        <Text style={styles.cardTitle}>{item.title}</Text>
        <Text style={styles.cardSubtitle}>{item.subtitle}</Text>
      </TouchableOpacity>
    </Animated.View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <Text style={styles.header}>请选择您想定制的sub愿景类别</Text>
        <Text style={styles.subheader}>让我们为您定制专属的肯定语</Text>
      </View>
      <FlatList
        data={categories}
        keyExtractor={(item) => item.id}
        numColumns={2}
        contentContainerStyle={styles.listContainer}
        columnWrapperStyle={styles.row}
        renderItem={renderItem}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: Spacing[6],
  },
  headerContainer: {
    alignItems: 'center',
    marginTop: Spacing[8],
    marginBottom: Spacing[8],
  },
  header: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.semibold,
    textAlign: 'center',
    color: Colors.text.primary,
    marginBottom: Spacing[2],
    lineHeight: Typography.lineHeight.normal * Typography.fontSize.xl,
  },
  subheader: {
    fontSize: Typography.fontSize.base,
    textAlign: 'center',
    color: Colors.text.secondary,
    lineHeight: Typography.lineHeight.relaxed * Typography.fontSize.base,
  },
  listContainer: {
    paddingVertical: Spacing[4],
  },
  row: {
    justifyContent: 'space-between',
    marginBottom: Spacing[4],
  },
  cardContainer: {
    width: '48%',
  },
  card: {
    ...ComponentStyles.card.elevated,
    aspectRatio: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: BorderRadius.lg,
    backgroundColor: Colors.background.primary,
    ...Shadows.md,
  },
  cardIcon: {
    fontSize: Typography.fontSize['3xl'],
    marginBottom: Spacing[2],
  },
  cardTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.text.primary,
    marginBottom: Spacing[1],
  },
  cardSubtitle: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
    textAlign: 'center',
  },
});
