// @ts-nocheck
// TopicScreen 已废弃，保留文件以防引用错误
import React, { useState } from 'react';
import BreathingBackground from '../components/BreathingBackground';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  Button,
  ScrollView,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/RootStack';

// 临时模拟可选场景数据，可根据实际接口替换
const TOPICS: { id: string; name: string }[] = [
  { id: 'scene1', name: '商务会议' },
  { id: 'scene2', name: '线上课堂' },
  { id: 'scene3', name: '播客录制' },
  { id: 'scene4', name: '游戏解说' },
];

type Props = NativeStackScreenProps<RootStackParamList, 'Topic'>;

export default function DeprecatedTopicScreen({ navigation, route }: Props) {
  const { categoryId } = route.params || {};
  const [selectedId, setSelectedId] = useState<string | null>(null);

  const handleNext = () => {
    if (!selectedId) return;
    navigation.navigate('Requirement', {
      categoryId: categoryId ?? '',
      topicId: selectedId,
    });
  };

  return (
    <BreathingBackground>
      <SafeAreaView style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {TOPICS.map((item) => {
          const selected = item.id === selectedId;
          return (
            <TouchableOpacity
              key={item.id}
              style={[styles.item, selected && styles.itemSelected]}
              activeOpacity={0.8}
              onPress={() => setSelectedId(item.id)}
            >
              <Text style={[styles.itemText, selected && styles.itemTextSelected]}>
                {item.name}
              </Text>
            </TouchableOpacity>
          );
        })}
      </ScrollView>

      <View style={styles.footer}>
        <Button title="下一步" onPress={handleNext} disabled={!selectedId} />
      </View>
      </SafeAreaView>
    </BreathingBackground>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
    justifyContent: 'center',
  },
  scrollContent: {
    alignItems: 'center',
    justifyContent: 'center',
    flexGrow: 1,
  },
  item: {
    borderWidth: 1,
    width: '100%',
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderColor: '#ccc',
    marginBottom: 12,
    backgroundColor: '#fff',
  },
  itemSelected: {
    borderColor: '#007AFF',
    backgroundColor: '#E6F0FF',
    borderWidth: 3,
  },
  itemText: {
    fontSize: 18,
    color: '#333',
    textAlign: 'center',
  },
  itemTextSelected: {
    color: '#007AFF',
    fontWeight: '600',
  },
  footer: {
    paddingVertical: 12,
  },
});
