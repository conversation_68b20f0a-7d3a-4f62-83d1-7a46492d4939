import React, { useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Animated } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/RootStack';
import { Colors, Typography, Spacing, Animation } from '../constants/DesignSystem';

type Props = NativeStackScreenProps<RootStackParamList, 'Splash'>;

export default function SplashScreen({ navigation }: Props) {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  useEffect(() => {
    // 启动动画序列
    Animated.sequence([
      // 延迟一点开始
      Animated.delay(300),
      // 同时执行淡入、缩放和滑动动画
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: Animation.duration.slower,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 50,
          friction: 8,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: Animation.duration.slower,
          useNativeDriver: true,
        }),
      ]),
      // 停留一段时间
      Animated.delay(1200),
      // 淡出动画
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: Animation.duration.normal,
        useNativeDriver: true,
      }),
    ]).start();

    // 导航到下一页
    const timer = setTimeout(() => {
      navigation.replace('Category');
    }, 3000);

    return () => clearTimeout(timer);
  }, [navigation, fadeAnim, scaleAnim, slideAnim]);

  return (
    <View style={styles.container}>
      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [
              { scale: scaleAnim },
              { translateY: slideAnim },
            ],
          },
        ]}
      >
        <View style={styles.logoContainer}>
          <Text style={styles.logo}>🎵</Text>
          <Text style={styles.appName}>音频SUB</Text>
        </View>
        <Text style={styles.subtitle}>让我们一起探索声音的力量</Text>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: Spacing[6],
  },
  content: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: Spacing[8],
  },
  logo: {
    fontSize: Typography.fontSize['5xl'],
    marginBottom: Spacing[2],
  },
  appName: {
    fontSize: Typography.fontSize['3xl'],
    fontWeight: Typography.fontWeight.bold,
    color: Colors.text.primary,
    letterSpacing: 2,
  },
  subtitle: {
    fontSize: Typography.fontSize.lg,
    textAlign: 'center',
    color: Colors.text.secondary,
    lineHeight: Typography.lineHeight.relaxed * Typography.fontSize.lg,
    maxWidth: 280,
  },
});
