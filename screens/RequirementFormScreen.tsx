import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  TouchableWithoutFeedback,
  Keyboard,
  KeyboardAvoidingView,
  ScrollView,
  Platform,
  Animated
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/RootStack';
import { Colors, Typography, Spacing, BorderRadius, ComponentStyles, Shadows } from '../constants/DesignSystem';

type Props = NativeStackScreenProps<RootStackParamList, 'Requirement'>;

export default function RequirementFormScreen({ navigation, route }: Props) {
  const [text, setText] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [showError, setShowError] = useState(false);
  const { categoryId } = route.params || {};

  const shakeAnim = useRef(new Animated.Value(0)).current;

  const handleTextChange = (newText: string) => {
    setText(newText);
    if (showError && newText.length >= 6) {
      setShowError(false);
    }
  };

  const handleNext = () => {
    if (text.trim().length < 6) {
      setShowError(true);
      // 震动动画
      Animated.sequence([
        Animated.timing(shakeAnim, { toValue: 10, duration: 100, useNativeDriver: true }),
        Animated.timing(shakeAnim, { toValue: -10, duration: 100, useNativeDriver: true }),
        Animated.timing(shakeAnim, { toValue: 10, duration: 100, useNativeDriver: true }),
        Animated.timing(shakeAnim, { toValue: 0, duration: 100, useNativeDriver: true }),
      ]).start();
      return;
    }
    navigation.navigate('BGM');
  };

  const getCategoryName = (id: string) => {
    const categoryMap: { [key: string]: string } = {
      'sp': 'SP特殊场景',
      'health': '健康调理',
      'biz': '外貌身材',
      'study': '学业提升',
      'fun': '财富磁铁',
      'mix': '多项愿景',
    };
    return categoryMap[id] || id;
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
      <KeyboardAvoidingView
        style={styles.keyboardView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          contentContainerStyle={styles.container}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.headerContainer}>
            <Text style={styles.categoryTag}>{getCategoryName(categoryId || '')}</Text>
            <Text style={styles.header}>写下3-5句关于显化目标的具体描述</Text>
            <Text style={styles.subheader}>请描述的尽量具体、详细</Text>
          </View>

          <View style={styles.inputContainer}>
            <TextInput
              style={[
                styles.input,
                isFocused && styles.inputFocused,
                showError && styles.inputError,
              ]}
              multiline
              numberOfLines={6}
              placeholder="例如：我想要显化完美的身材。我希望我怎么吃都吃不胖。我想腰更细，体态更好。"
              placeholderTextColor={Colors.text.placeholder}
              value={text}
              onChangeText={handleTextChange}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              textAlignVertical="top"
            />
            <View style={styles.inputFooter}>
              <Text style={[styles.charCount, text.length < 6 && styles.charCountError]}>
                {text.length}/200 字符 {text.length < 6 && '(至少6个字符)'}
              </Text>
            </View>
          </View>

          {showError && (
            <Animated.View style={[styles.errorContainer, { transform: [{ translateX: shakeAnim }] }]}>
              <Text style={styles.errorText}>请至少输入6个字符来描述你的需求</Text>
            </Animated.View>
          )}

          <TouchableOpacity
            style={[
              styles.nextButton,
              text.trim().length >= 6 ? styles.nextButtonActive : styles.nextButtonDisabled,
            ]}
            onPress={handleNext}
            disabled={text.trim().length < 6}
          >
            <Text style={[
              styles.nextButtonText,
              text.trim().length >= 6 ? styles.nextButtonTextActive : styles.nextButtonTextDisabled,
            ]}>
              下一步
            </Text>
          </TouchableOpacity>
        </ScrollView>
      </KeyboardAvoidingView>
    </TouchableWithoutFeedback>
  );
}

const styles = StyleSheet.create({
  keyboardView: {
    flex: 1,
  },
  container: {
    flexGrow: 1,
    padding: Spacing[6],
    justifyContent: 'center',
  },
  headerContainer: {
    alignItems: 'center',
    marginBottom: Spacing[8],
  },
  categoryTag: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.primary[600],
    backgroundColor: Colors.primary[100],
    paddingHorizontal: Spacing[3],
    paddingVertical: Spacing[1],
    borderRadius: BorderRadius.full,
    marginBottom: Spacing[4],
  },
  header: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.semibold,
    textAlign: 'center',
    color: Colors.text.primary,
    marginBottom: Spacing[2],
    lineHeight: Typography.lineHeight.normal * Typography.fontSize.xl,
  },
  subheader: {
    fontSize: Typography.fontSize.base,
    textAlign: 'center',
    color: Colors.text.secondary,
    lineHeight: Typography.lineHeight.relaxed * Typography.fontSize.base,
  },
  inputContainer: {
    width: '100%',
    marginBottom: Spacing[6],
  },
  input: {
    ...ComponentStyles.input.base,
    height: 140,
    fontSize: Typography.fontSize.base,
    lineHeight: Typography.lineHeight.relaxed * Typography.fontSize.base,
    backgroundColor: Colors.background.primary,
    ...Shadows.sm,
  },
  inputFocused: {
    ...ComponentStyles.input.focused,
    borderColor: Colors.primary[400],
    ...Shadows.base,
  },
  inputError: {
    borderColor: Colors.error,
    backgroundColor: '#FFF5F5',
  },
  inputFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: Spacing[2],
  },
  charCount: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.tertiary,
  },
  charCountError: {
    color: Colors.error,
  },
  errorContainer: {
    backgroundColor: '#FFF5F5',
    borderColor: Colors.error,
    borderWidth: 1,
    borderRadius: BorderRadius.base,
    padding: Spacing[3],
    marginBottom: Spacing[4],
  },
  errorText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.error,
    textAlign: 'center',
  },
  nextButton: {
    borderRadius: BorderRadius.base,
    paddingHorizontal: Spacing[8],
    paddingVertical: Spacing[4],
    alignItems: 'center',
    ...Shadows.sm,
  },
  nextButtonActive: {
    backgroundColor: Colors.primary[400],
  },
  nextButtonDisabled: {
    backgroundColor: Colors.neutral[300],
  },
  nextButtonText: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
  },
  nextButtonTextActive: {
    color: Colors.text.inverse,
  },
  nextButtonTextDisabled: {
    color: Colors.text.tertiary,
  },
});
