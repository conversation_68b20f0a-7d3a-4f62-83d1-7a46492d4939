import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert, ScrollView } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/RootStack';
import * as DocumentPicker from 'expo-document-picker';
import { Colors, Typography, Spacing, BorderRadius, Shadows, ComponentStyles } from '../constants/DesignSystem';

type Props = NativeStackScreenProps<RootStackParamList, 'CustomAudio'>;

export default function CustomAudioScreen({ navigation }: Props) {
  const [selectedAudio, setSelectedAudio] = useState<DocumentPicker.DocumentPickerResult | null>(null);

  const pickAudio = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: ['audio/mpeg', 'audio/wav', 'audio/mp3'],
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setSelectedAudio(result);
        Alert.alert('上传成功', `已选择音频文件: ${result.assets[0].name}`);
      }
    } catch (error) {
      Alert.alert('上传失败', '音频文件上传出现问题，请重试');
    }
  };

  const handleNext = () => {
    if (!selectedAudio) {
      Alert.alert('请选择音频', '请先上传一个音频文件');
      return;
    }
    navigation.navigate('Processing');
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.headerContainer}>
        <Text style={styles.header}>上传您的自定义音频</Text>
        <Text style={styles.subheader}>支持MP3和WAV格式的音频文件</Text>
      </View>

      <View style={styles.uploadContainer}>
        <TouchableOpacity style={styles.uploadButton} onPress={pickAudio}>
          <View style={styles.uploadIcon}>
            <Text style={styles.uploadIconText}>📁</Text>
          </View>
          <Text style={styles.uploadButtonText}>选择音频文件</Text>
          <Text style={styles.uploadHint}>点击选择MP3或WAV文件</Text>
        </TouchableOpacity>

        {selectedAudio && selectedAudio.assets && selectedAudio.assets[0] && (
          <View style={styles.selectedFileContainer}>
            <View style={styles.fileInfo}>
              <Text style={styles.fileIcon}>🎵</Text>
              <View style={styles.fileDetails}>
                <Text style={styles.fileName}>{selectedAudio.assets[0].name}</Text>
                <Text style={styles.fileSize}>
                  {selectedAudio.assets[0].size ? 
                    `${(selectedAudio.assets[0].size / 1024 / 1024).toFixed(2)} MB` : 
                    '未知大小'
                  }
                </Text>
              </View>
            </View>
          </View>
        )}
      </View>

      <View style={styles.actionsContainer}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>返回</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.nextButton,
            selectedAudio ? styles.nextButtonActive : styles.nextButtonDisabled,
          ]}
          onPress={handleNext}
          disabled={!selectedAudio}
        >
          <Text style={[
            styles.nextButtonText,
            selectedAudio ? styles.nextButtonTextActive : styles.nextButtonTextDisabled,
          ]}>
            下一步
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: Spacing[6],
  },
  headerContainer: {
    alignItems: 'center',
    marginTop: Spacing[8],
    marginBottom: Spacing[8],
  },
  header: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.semibold,
    textAlign: 'center',
    color: Colors.text.primary,
    marginBottom: Spacing[2],
    lineHeight: Typography.lineHeight.normal * Typography.fontSize.xl,
  },
  subheader: {
    fontSize: Typography.fontSize.base,
    textAlign: 'center',
    color: Colors.text.secondary,
    lineHeight: Typography.lineHeight.relaxed * Typography.fontSize.base,
  },
  uploadContainer: {
    alignItems: 'center',
    marginBottom: Spacing[8],
  },
  uploadButton: {
    ...ComponentStyles.card.elevated,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    minHeight: 200,
    borderRadius: BorderRadius.lg,
    borderWidth: 2,
    borderColor: Colors.primary[300],
    borderStyle: 'dashed',
    backgroundColor: Colors.background.tertiary,
  },
  uploadIcon: {
    marginBottom: Spacing[4],
  },
  uploadIconText: {
    fontSize: Typography.fontSize['4xl'],
  },
  uploadButtonText: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.text.primary,
    marginBottom: Spacing[2],
  },
  uploadHint: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
    textAlign: 'center',
  },
  selectedFileContainer: {
    width: '100%',
    marginTop: Spacing[4],
  },
  fileInfo: {
    ...ComponentStyles.card.base,
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing[4],
    backgroundColor: Colors.primary[50],
    borderColor: Colors.primary[200],
    borderWidth: 1,
  },
  fileIcon: {
    fontSize: Typography.fontSize['2xl'],
    marginRight: Spacing[3],
  },
  fileDetails: {
    flex: 1,
  },
  fileName: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.text.primary,
    marginBottom: Spacing[1],
  },
  fileSize: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: Spacing[4],
  },
  backButton: {
    ...ComponentStyles.button.secondary,
    flex: 1,
    alignItems: 'center',
    paddingVertical: Spacing[4],
    backgroundColor: Colors.background.primary,
    borderColor: Colors.neutral[400],
  },
  backButtonText: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.text.secondary,
  },
  nextButton: {
    flex: 1,
    borderRadius: BorderRadius.base,
    paddingVertical: Spacing[4],
    alignItems: 'center',
    ...Shadows.sm,
  },
  nextButtonActive: {
    backgroundColor: Colors.primary[400],
  },
  nextButtonDisabled: {
    backgroundColor: Colors.neutral[300],
  },
  nextButtonText: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.semibold,
  },
  nextButtonTextActive: {
    color: Colors.text.inverse,
  },
  nextButtonTextDisabled: {
    color: Colors.text.tertiary,
  },
});
