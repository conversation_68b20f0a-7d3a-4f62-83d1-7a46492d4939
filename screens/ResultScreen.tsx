import React, { useRef, useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated, Alert } from 'react-native';
import { Audio } from 'expo-av';
import Slider from '@react-native-community/slider';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/RootStack';
import * as Sharing from 'expo-sharing';
import * as FileSystem from 'expo-file-system';
import { Colors, Typography, Spacing, BorderRadius, Shadows } from '../constants/DesignSystem';

const demoAudio = require('../assets/demo.mp3');

type Props = NativeStackScreenProps<RootStackParamList, 'Result'>;

export default function ResultScreen({ navigation }: Props) {
  const soundRef = useRef<Audio.Sound | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [positionMillis, setPositionMillis] = useState(0);
  const [durationMillis, setDurationMillis] = useState(1);
  const [isLoading, setIsLoading] = useState(false);

  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;
  const playButtonScale = useRef(new Animated.Value(1)).current;

  // 入场动画
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  }, [fadeAnim, scaleAnim]);

  // 清理：组件卸载时卸载音频资源
  useEffect(() => {
    return () => {
      soundRef.current && soundRef.current.unloadAsync();
    };
  }, []);

  const animatePlayButton = () => {
    Animated.sequence([
      Animated.spring(playButtonScale, {
        toValue: 0.9,
        useNativeDriver: true,
      }),
      Animated.spring(playButtonScale, {
        toValue: 1,
        useNativeDriver: true,
      }),
    ]).start();
  };

  async function togglePlayback() {
    try {
      setIsLoading(true);
      animatePlayButton();

      if (!soundRef.current) {
        await Audio.setAudioModeAsync({
          allowsRecordingIOS: false,
          staysActiveInBackground: false,
          playsInSilentModeIOS: true,
        });
        const { sound } = await Audio.Sound.createAsync(demoAudio, {}, onUpdate);
        soundRef.current = sound;
        await sound.playAsync();
        setIsPlaying(true);
        setIsLoading(false);
        return;
      }
      if (isPlaying) {
        await soundRef.current.pauseAsync();
        setIsPlaying(false);
      } else {
        await soundRef.current.playAsync();
        setIsPlaying(true);
      }
      setIsLoading(false);
    } catch (error: any) {
      console.error('Audio error:', error?.message || error);
      setIsLoading(false);
      Alert.alert('播放错误', '音频播放出现问题，请重试');
    }
  }

  function onUpdate(status: any) {
    if ('positionMillis' in status && 'durationMillis' in status) {
      setPositionMillis(status.positionMillis);
      setDurationMillis(status.durationMillis || 1);
    }
  }

  async function onDownload() {
    try {
      // 获取音频文件的URI
      const audioUri = demoAudio;

      // 创建目标文件路径
      const fileName = '专属sub音频.mp3';
      const fileUri = FileSystem.documentDirectory + fileName;

      // 复制文件到文档目录
      await FileSystem.copyAsync({
        from: audioUri,
        to: fileUri,
      });

      // 分享文件
      const available = await Sharing.isAvailableAsync();
      if (available) {
        await Sharing.shareAsync(fileUri, {
          mimeType: 'audio/mpeg',
          dialogTitle: '保存专属sub音频',
        });
        Alert.alert('下载成功', `音频已保存为：${fileName}`);
      } else {
        Alert.alert('下载完成', `音频已保存到应用文档目录：${fileName}`);
      }
    } catch (error) {
      console.error('Download error:', error);
      Alert.alert('下载失败', '音频下载出现问题，请重试');
    }
  }

  const formatTime = (millis: number) => {
    const minutes = Math.floor(millis / 60000);
    const seconds = Math.floor((millis % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <View style={styles.container}>
      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        {/* 音乐图标 */}
        <View style={styles.musicIconContainer}>
          <View style={styles.musicIcon}>
            <Text style={styles.musicIconText}>♪</Text>
          </View>
        </View>

        {/* 标题区域 */}
        <View style={styles.headerContainer}>
          <Text style={styles.header}>你的疗愈音频已准备好</Text>
          <Text style={styles.subheader}>专为你定制的声音疗愈体验</Text>
        </View>

        {/* 播放器区域 */}
        <View style={styles.playerContainer}>
          <Animated.View style={{ transform: [{ scale: playButtonScale }] }}>
            <TouchableOpacity
              style={styles.playButton}
              onPress={togglePlayback}
              disabled={isLoading}
            >
              {isLoading ? (
                <View style={styles.loadingDot} />
              ) : isPlaying ? (
                <View style={styles.pauseIcon}>
                  <View style={styles.pauseBar} />
                  <View style={styles.pauseBar} />
                </View>
              ) : (
                <View style={styles.playIcon} />
              )}
            </TouchableOpacity>
          </Animated.View>

          {/* 进度条和时间 */}
          <View style={styles.progressContainer}>
            <View style={styles.timeContainer}>
              <Text style={styles.timeText}>{formatTime(positionMillis)}</Text>
              <Text style={styles.timeText}>{formatTime(durationMillis)}</Text>
            </View>
            <Slider
              style={styles.slider}
              minimumValue={0}
              maximumValue={durationMillis}
              value={positionMillis}
              minimumTrackTintColor={Colors.primary[400]}
              maximumTrackTintColor={Colors.neutral[300]}
              disabled
            />
          </View>
        </View>

        {/* 操作按钮 */}
        <View style={styles.actionsContainer}>
          <TouchableOpacity style={styles.actionButton} onPress={onDownload}>
            <Text style={styles.actionIcon}>↓</Text>
            <Text style={styles.actionButtonText}>Download</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigation.reset({ index: 0, routes: [{ name: 'Category' }] })}
          >
            <Text style={styles.actionIcon}>↻</Text>
            <Text style={styles.actionButtonText}>Redesign</Text>
          </TouchableOpacity>
        </View>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: Spacing[6],
  },
  content: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  musicIconContainer: {
    marginBottom: Spacing[8],
  },
  musicIcon: {
    width: 80,
    height: 80,
    borderRadius: 20,
    backgroundColor: Colors.primary[300],
    alignItems: 'center',
    justifyContent: 'center',
    ...Shadows.base,
  },
  musicIconText: {
    fontSize: 40,
    color: Colors.text.inverse,
    fontWeight: Typography.fontWeight.bold,
  },
  headerContainer: {
    alignItems: 'center',
    marginBottom: Spacing[10],
  },
  header: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: Typography.fontWeight.bold,
    color: Colors.text.primary,
    textAlign: 'center',
    marginBottom: Spacing[3],
    lineHeight: Typography.lineHeight.normal * Typography.fontSize['2xl'],
  },
  subheader: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.secondary,
    textAlign: 'center',
    lineHeight: Typography.lineHeight.relaxed * Typography.fontSize.base,
  },
  playerContainer: {
    alignItems: 'center',
    marginBottom: Spacing[12],
    width: '100%',
  },
  playButton: {
    width: 120,
    height: 120,
    borderRadius: BorderRadius.full,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Spacing[8],
    ...Shadows.lg,
  },
  loadingDot: {
    width: 12,
    height: 12,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.primary[400],
  },
  pauseIcon: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 4,
  },
  pauseBar: {
    width: 6,
    height: 24,
    backgroundColor: Colors.primary[500],
    borderRadius: 3,
  },
  playIcon: {
    width: 0,
    height: 0,
    borderLeftWidth: 18,
    borderRightWidth: 0,
    borderTopWidth: 12,
    borderBottomWidth: 12,
    borderLeftColor: Colors.primary[500],
    borderTopColor: 'transparent',
    borderBottomColor: 'transparent',
    marginLeft: 4,
  },
  progressContainer: {
    width: '100%',
    alignItems: 'center',
  },
  timeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '85%',
    marginBottom: Spacing[2],
  },
  timeText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
    fontWeight: Typography.fontWeight.medium,
  },
  slider: {
    width: '85%',
    height: 40,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
    gap: Spacing[8],
  },
  actionButton: {
    alignItems: 'center',
    paddingVertical: Spacing[3],
    paddingHorizontal: Spacing[6],
    backgroundColor: 'transparent',
    minWidth: 100,
  },
  actionIcon: {
    fontSize: Typography.fontSize.lg,
    color: Colors.text.primary,
    marginBottom: Spacing[1],
    fontWeight: Typography.fontWeight.bold,
  },
  actionButtonText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.text.primary,
    textAlign: 'center',
  },
});
