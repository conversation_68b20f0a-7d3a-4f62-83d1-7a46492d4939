import React, { useRef, useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated, Alert } from 'react-native';
import { Audio } from 'expo-av';
import Slider from '@react-native-community/slider';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/RootStack';
import * as Sharing from 'expo-sharing';
import * as FileSystem from 'expo-file-system';
import { Colors, Typography, Spacing, BorderRadius, Shadows, ComponentStyles } from '../constants/DesignSystem';

const demoAudio = require('../assets/demo.mp3');

type Props = NativeStackScreenProps<RootStackParamList, 'Result'>;

export default function ResultScreen({ navigation }: Props) {
  const soundRef = useRef<Audio.Sound | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [positionMillis, setPositionMillis] = useState(0);
  const [durationMillis, setDurationMillis] = useState(1);
  const [isLoading, setIsLoading] = useState(false);

  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;
  const playButtonScale = useRef(new Animated.Value(1)).current;

  // 入场动画
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  }, [fadeAnim, scaleAnim]);

  // 清理：组件卸载时卸载音频资源
  useEffect(() => {
    return () => {
      soundRef.current && soundRef.current.unloadAsync();
    };
  }, []);

  const animatePlayButton = () => {
    Animated.sequence([
      Animated.spring(playButtonScale, {
        toValue: 0.9,
        useNativeDriver: true,
      }),
      Animated.spring(playButtonScale, {
        toValue: 1,
        useNativeDriver: true,
      }),
    ]).start();
  };

  async function togglePlayback() {
    try {
      setIsLoading(true);
      animatePlayButton();

      if (!soundRef.current) {
        await Audio.setAudioModeAsync({
          allowsRecordingIOS: false,
          staysActiveInBackground: false,
          playsInSilentModeIOS: true,
        });
        const { sound } = await Audio.Sound.createAsync(demoAudio, {}, onUpdate);
        soundRef.current = sound;
        await sound.playAsync();
        setIsPlaying(true);
        setIsLoading(false);
        return;
      }
      if (isPlaying) {
        await soundRef.current.pauseAsync();
        setIsPlaying(false);
      } else {
        await soundRef.current.playAsync();
        setIsPlaying(true);
      }
      setIsLoading(false);
    } catch (error: any) {
      console.error('Audio error:', error?.message || error);
      setIsLoading(false);
      Alert.alert('播放错误', '音频播放出现问题，请重试');
    }
  }

  function onUpdate(status: any) {
    if ('positionMillis' in status && 'durationMillis' in status) {
      setPositionMillis(status.positionMillis);
      setDurationMillis(status.durationMillis || 1);
    }
  }

  async function onDownload() {
    try {
      // 获取音频文件的URI
      const audioUri = demoAudio;

      // 创建目标文件路径
      const fileName = '专属sub音频.mp3';
      const fileUri = FileSystem.documentDirectory + fileName;

      // 复制文件到文档目录
      await FileSystem.copyAsync({
        from: audioUri,
        to: fileUri,
      });

      // 分享文件
      const available = await Sharing.isAvailableAsync();
      if (available) {
        await Sharing.shareAsync(fileUri, {
          mimeType: 'audio/mpeg',
          dialogTitle: '保存专属sub音频',
        });
        Alert.alert('下载成功', `音频已保存为：${fileName}`);
      } else {
        Alert.alert('下载完成', `音频已保存到应用文档目录：${fileName}`);
      }
    } catch (error) {
      console.error('Download error:', error);
      Alert.alert('下载失败', '音频下载出现问题，请重试');
    }
  }

  const formatTime = (millis: number) => {
    const minutes = Math.floor(millis / 60000);
    const seconds = Math.floor((millis % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <View style={styles.container}>
      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        <View style={styles.headerContainer}>
          <Text style={styles.successIcon}>🎵</Text>
          <Text style={styles.header}>你的疗愈音频已准备好</Text>
          <Text style={styles.subheader}>专为你定制的声音疗愈体验</Text>
        </View>

        <View style={styles.playerContainer}>
          <Animated.View style={{ transform: [{ scale: playButtonScale }] }}>
            <TouchableOpacity
              style={[styles.playButton, isPlaying && styles.playButtonActive]}
              onPress={togglePlayback}
              disabled={isLoading}
            >
              <View style={styles.playButtonIcon}>
                {isLoading ? (
                  <View style={styles.loadingDot} />
                ) : isPlaying ? (
                  <View style={styles.pauseIcon}>
                    <View style={styles.pauseBar} />
                    <View style={styles.pauseBar} />
                  </View>
                ) : (
                  <View style={styles.playIcon} />
                )}
              </View>
            </TouchableOpacity>
          </Animated.View>

          <View style={styles.progressContainer}>
            <Slider
              style={styles.slider}
              minimumValue={0}
              maximumValue={durationMillis}
              value={positionMillis}
              minimumTrackTintColor={Colors.primary[400]}
              maximumTrackTintColor={Colors.neutral[300]}

              disabled
            />
            <View style={styles.timeContainer}>
              <Text style={styles.timeText}>{formatTime(positionMillis)}</Text>
              <Text style={styles.timeText}>{formatTime(durationMillis)}</Text>
            </View>
          </View>
        </View>

        <View style={styles.actionsContainer}>
          <TouchableOpacity style={styles.actionButton} onPress={onDownload}>
            <View style={styles.downloadIconContainer}>
              <View style={styles.downloadIcon}>
                <View style={styles.downloadArrow} />
                <View style={styles.downloadLine} />
              </View>
            </View>
            <Text style={styles.actionButtonText}>下载</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigation.reset({ index: 0, routes: [{ name: 'Category' }] })}
          >
            <View style={styles.refreshIconContainer}>
              <View style={styles.refreshIcon}>
                <View style={styles.refreshArrow1} />
                <View style={styles.refreshArrow2} />
              </View>
            </View>
            <Text style={styles.actionButtonText}>重新定制</Text>
          </TouchableOpacity>
        </View>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: Spacing[6],
  },
  content: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  headerContainer: {
    alignItems: 'center',
    marginBottom: Spacing[8],
  },
  successIcon: {
    fontSize: Typography.fontSize['4xl'],
    marginBottom: Spacing[3],
  },
  header: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.text.primary,
    textAlign: 'center',
    marginBottom: Spacing[2],
    lineHeight: Typography.lineHeight.normal * Typography.fontSize.xl,
  },
  subheader: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.secondary,
    textAlign: 'center',
    lineHeight: Typography.lineHeight.relaxed * Typography.fontSize.base,
  },
  playerContainer: {
    alignItems: 'center',
    marginBottom: Spacing[8],
    width: '100%',
  },
  playButton: {
    width: 80,
    height: 80,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.background.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Spacing[6],
    ...Shadows.lg,
  },
  playButtonActive: {
    backgroundColor: Colors.primary[100],
  },
  playButtonIcon: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingDot: {
    width: 8,
    height: 8,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.primary[400],
  },
  pauseIcon: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 3,
  },
  pauseBar: {
    width: 4,
    height: 16,
    backgroundColor: Colors.primary[600],
    borderRadius: 2,
  },
  playIcon: {
    width: 0,
    height: 0,
    borderLeftWidth: 12,
    borderRightWidth: 0,
    borderTopWidth: 8,
    borderBottomWidth: 8,
    borderLeftColor: Colors.primary[600],
    borderTopColor: 'transparent',
    borderBottomColor: 'transparent',
    marginLeft: 3,
  },
  progressContainer: {
    width: '100%',
    alignItems: 'center',
  },
  slider: {
    width: '90%',
    height: 40,
  },

  timeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '90%',
    marginTop: Spacing[1],
  },
  timeText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
    fontWeight: Typography.fontWeight.medium,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    gap: Spacing[4],
  },
  actionButton: {
    ...ComponentStyles.button.secondary,
    flex: 1,
    alignItems: 'center',
    paddingVertical: Spacing[4],
    backgroundColor: Colors.background.primary,
    borderColor: Colors.primary[300],
    ...Shadows.base,
  },
  downloadIconContainer: {
    marginBottom: Spacing[1],
  },
  downloadIcon: {
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  downloadArrow: {
    width: 0,
    height: 0,
    borderLeftWidth: 4,
    borderRightWidth: 4,
    borderTopWidth: 8,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderTopColor: Colors.primary[500],
    marginBottom: 2,
  },
  downloadLine: {
    width: 16,
    height: 2,
    backgroundColor: Colors.primary[500],
    borderRadius: 1,
  },
  refreshIconContainer: {
    marginBottom: Spacing[1],
  },
  refreshIcon: {
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  refreshArrow1: {
    position: 'absolute',
    width: 0,
    height: 0,
    borderLeftWidth: 3,
    borderRightWidth: 3,
    borderBottomWidth: 6,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderBottomColor: Colors.primary[500],
    top: 2,
    right: 2,
    transform: [{ rotate: '45deg' }],
  },
  refreshArrow2: {
    position: 'absolute',
    width: 0,
    height: 0,
    borderLeftWidth: 3,
    borderRightWidth: 3,
    borderTopWidth: 6,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderTopColor: Colors.primary[500],
    bottom: 2,
    left: 2,
    transform: [{ rotate: '45deg' }],
  },
  actionButtonText: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.text.primary,
  },
});
