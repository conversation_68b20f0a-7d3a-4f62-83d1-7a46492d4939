import { StatusBar } from 'expo-status-bar';
import { StyleSheet } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import BreathingBackground from './components/BreathingBackground';
import RootStack from './navigation/RootStack';

export default function App() {
  return (
    <BreathingBackground>
      <NavigationContainer>
        <RootStack />
      </NavigationContainer>
      <StatusBar style="light" />
    </BreathingBackground>
  );
}

const styles = StyleSheet.create({});
